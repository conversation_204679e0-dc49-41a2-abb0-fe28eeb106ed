import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import { ENV } from '$lib/config/env';

export const GET: RequestHandler = async ({ cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		await connect();

		// Decode token to get current user
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const currentUser = await User.findById(decoded.userId);

		if (!currentUser) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// Find all users except the current user using the username
		const otherUsers = await User.find(
			{
				username: { $ne: currentUser.username }
			},
			'username'
		);

		return json({ users: otherUsers.map((user) => user.username) });
	} catch (err) {
		console.error('Error fetching users:', err);
		return json({ error: 'Failed to fetch users' }, { status: 500 });
	}
};
