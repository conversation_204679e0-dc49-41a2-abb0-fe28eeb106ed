import { S3<PERSON>lient, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { dev } from '$app/environment';

// R2 Configuration
const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID;
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY;
const R2_BUCKET_NAME = process.env.R2_BUCKET_NAME || 'konekt';
const R2_PUBLIC_URL = process.env.R2_PUBLIC_URL;

// Check if R2 is properly configured
const isR2Configured = R2_ACCOUNT_ID &&
	R2_ACCESS_KEY_ID &&
	R2_SECRET_ACCESS_KEY &&
	R2_PUBLIC_URL &&
	R2_ACCOUNT_ID !== 'demo_account_id' &&
	R2_ACCESS_KEY_ID !== 'demo_access_key' &&
	R2_ACCOUNT_ID !== 'dev_mode' &&
	R2_ACCESS_KEY_ID !== 'dev_mode';

// Create S3 client configured for Cloudflare R2 (only if properly configured)
let r2Client: S3Client | null = null;

if (isR2Configured) {
	try {
		r2Client = new S3Client({
			region: 'auto',
			endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
			credentials: {
				accessKeyId: R2_ACCESS_KEY_ID!,
				secretAccessKey: R2_SECRET_ACCESS_KEY!,
			},
		});
		console.log('✅ R2 storage configured and ready');
	} catch (error) {
		console.warn('Failed to initialize R2 client:', error);
		r2Client = null;
	}
} else {
	console.log('📁 R2 storage not configured - using development mode with mock uploads');
	console.log('   To enable real file uploads, configure R2 credentials in .env (see .env.example)');
}

export interface UploadResult {
	key: string;
	url: string;
	size: number;
	contentType: string;
}

export class R2StorageService {
	private static instance: R2StorageService;

	static getInstance(): R2StorageService {
		if (!R2StorageService.instance) {
			R2StorageService.instance = new R2StorageService();
		}
		return R2StorageService.instance;
	}

	/**
	 * Upload a file to R2 storage
	 */
	async uploadFile(
		file: File | Buffer,
		key: string,
		contentType: string,
		metadata?: Record<string, string>
	): Promise<UploadResult> {
		// Check if R2 is properly configured
		if (!isR2Configured || !r2Client) {
			console.warn('R2 not configured - using mock upload');
			// Return a mock result for development
			const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file;
			return {
				key,
				url: `https://via.placeholder.com/150x150.png?text=Mock+File`, // Placeholder image
				size: buffer.length,
				contentType,
			};
		}

		try {
			const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file;

			const command = new PutObjectCommand({
				Bucket: R2_BUCKET_NAME,
				Key: key,
				Body: buffer,
				ContentType: contentType,
				Metadata: metadata,
			});

			await r2Client.send(command);

			return {
				key,
				url: `${R2_PUBLIC_URL}/${key}`,
				size: buffer.length,
				contentType,
			};
		} catch (error) {
			console.error('Error uploading file to R2:', error);
			console.warn('R2 upload failed - falling back to mock upload');

			// Fallback to mock upload if R2 fails
			const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file;
			return {
				key,
				url: `https://via.placeholder.com/150x150.png?text=Upload+Failed`, // Placeholder image
				size: buffer.length,
				contentType,
			};
		}
	}

	/**
	 * Delete a file from R2 storage
	 */
	async deleteFile(key: string): Promise<void> {
		if (!isR2Configured || !r2Client) {
			console.warn('R2 not configured - skipping file deletion');
			return;
		}

		try {
			const command = new DeleteObjectCommand({
				Bucket: R2_BUCKET_NAME,
				Key: key,
			});

			await r2Client.send(command);
		} catch (error) {
			console.error('Error deleting file from R2:', error);
			throw new Error('Failed to delete file');
		}
	}

	/**
	 * Generate a presigned URL for direct upload (optional, for large files)
	 */
	async getPresignedUploadUrl(key: string, contentType: string, expiresIn = 3600): Promise<string> {
		if (!isR2Configured || !r2Client) {
			throw new Error('R2 not configured - cannot generate presigned URL');
		}

		try {
			const command = new PutObjectCommand({
				Bucket: R2_BUCKET_NAME,
				Key: key,
				ContentType: contentType,
			});

			return await getSignedUrl(r2Client, command, { expiresIn });
		} catch (error) {
			console.error('Error generating presigned URL:', error);
			throw new Error('Failed to generate upload URL');
		}
	}

	/**
	 * Generate a unique file key
	 */
	generateFileKey(userId: string, type: 'avatar' | 'attachment', originalName: string): string {
		const timestamp = Date.now();
		const randomString = Math.random().toString(36).substring(2, 15);
		const extension = originalName.split('.').pop();

		return `${type}s/${userId}/${timestamp}-${randomString}.${extension}`;
	}

	/**
	 * Validate file type and size
	 */
	validateFile(file: File, type: 'avatar' | 'attachment'): { valid: boolean; error?: string } {
		const maxSize = 5 * 1024 * 1024; // 5MB

		if (file.size > maxSize) {
			return { valid: false, error: 'File size must be less than 5MB' };
		}

		if (type === 'avatar') {
			const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
			if (!allowedTypes.includes(file.type)) {
				return { valid: false, error: 'Avatar must be an image (JPEG, PNG, GIF, or WebP)' };
			}
		} else if (type === 'attachment') {
			const allowedTypes = [
				// Images
				'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
				// Documents
				'application/pdf', 'text/plain', 'application/msword',
				'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
				// Archives
				'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
				// Other
				'application/json', 'text/csv'
			];

			if (!allowedTypes.includes(file.type)) {
				return { valid: false, error: 'File type not supported' };
			}
		}

		return { valid: true };
	}

	/**
	 * Get file URL from key
	 */
	getFileUrl(key: string): string {
		if (!isR2Configured || !R2_PUBLIC_URL) {
			return `https://via.placeholder.com/150x150.png?text=Mock+File`;
		}
		return `${R2_PUBLIC_URL}/${key}`;
	}
}

export default R2StorageService.getInstance();
