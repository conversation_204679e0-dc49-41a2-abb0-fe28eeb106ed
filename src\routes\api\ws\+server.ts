import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import { addConnection, removeConnection } from '$lib/services/realtime';
import { ENV } from '$lib/config/env';

export const GET: RequestHandler = async ({ request, url }) => {
	// Get token from query params for SSE authentication
	const token = url.searchParams.get('token');

	if (!token) {
		return new Response('Token required', { status: 401 });
	}

	try {
		// Verify JWT token
		await connect();
		const decoded = jwt.verify(token, ENV.JWT_SECRET) as any;
		const user = await User.findById(decoded.userId);

		if (!user) {
			return new Response('Invalid user', { status: 401 });
		}

		// Create SSE stream
		const stream = new ReadableStream({
			start(controller) {
				const connectionId = `${user._id}_${Date.now()}`;

				// Store connection
				addConnection(connectionId, {
					controller,
					username: user.username,
					userId: user._id.toString()
				});

				console.log(`User ${user.username} connected via SSE`);

				// Send initial connection success message
				const welcomeMessage = `data: ${JSON.stringify({
					type: 'connected',
					username: user.username
				})}\n\n`;
				controller.enqueue(new TextEncoder().encode(welcomeMessage));

				// Handle connection cleanup
				const cleanup = () => {
					removeConnection(connectionId);
					console.log(`User ${user.username} disconnected from SSE`);
				};

				// Cleanup on stream close
				request.signal.addEventListener('abort', cleanup);
			}
		});

		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				'Connection': 'keep-alive',
				'Access-Control-Allow-Origin': '*',
				'Access-Control-Allow-Headers': 'Cache-Control'
			}
		});

	} catch (error) {
		console.error('SSE authentication error:', error);
		return new Response('Authentication failed', { status: 401 });
	}
};
