import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import Chat from '$lib/models/Chat';
import r2Storage from '$lib/services/r2Storage';
import { ENV } from '$lib/config/env';

export const GET: RequestHandler = async ({ params, cookies, url }) => {
	const { key } = params;
	const token = cookies.get('token');

	if (!token) {
		return new Response('Unauthorized', { status: 401 });
	}

	if (!key) {
		return new Response('File key required', { status: 400 });
	}

	try {
		// Verify JWT token
		await connect();
		const decoded = jwt.verify(token, ENV.JWT_SECRET) as any;
		const user = await User.findById(decoded.userId);

		if (!user) {
			return new Response('Invalid user', { status: 401 });
		}

		// Check if user has access to this file
		// Look for the file in chat messages where user is sender/receiver or group member
		const fileAccess = await Chat.findOne({
			'attachments.key': key,
			$or: [
				{ sender: user._id }, // User sent the message
				{ receiver: user._id }, // User received the message
				{ 
					group: { $exists: true },
					// For group messages, we need to check if user is a group member
					// This will be handled by a separate query below
				}
			]
		}).populate('group');

		// If not found in direct messages, check group membership
		if (!fileAccess) {
			const groupMessage = await Chat.findOne({
				'attachments.key': key,
				group: { $exists: true }
			}).populate({
				path: 'group',
				populate: {
					path: 'members',
					select: '_id'
				}
			});

			if (groupMessage && groupMessage.group) {
				const group = groupMessage.group as any;
				const isMember = group.members.some((member: any) => 
					member._id.toString() === user._id.toString()
				);
				
				if (!isMember) {
					return new Response('Access denied', { status: 403 });
				}
			} else {
				return new Response('File not found or access denied', { status: 404 });
			}
		}

		// Get file from R2
		const fileData = await r2Storage.getFile(key);
		
		if (!fileData) {
			return new Response('File not found', { status: 404 });
		}

		// Get the attachment info to determine content type
		const message = await Chat.findOne({ 'attachments.key': key });
		const attachment = message?.attachments?.find((att: any) => att.key === key);
		
		const contentType = attachment?.contentType || 'application/octet-stream';
		const filename = attachment?.filename || 'download';

		// Check if it's an image for inline display
		const isImage = contentType.startsWith('image/');
		const disposition = isImage ? 'inline' : `attachment; filename="${filename}"`;

		return new Response(fileData, {
			headers: {
				'Content-Type': contentType,
				'Content-Disposition': disposition,
				'Cache-Control': 'private, max-age=3600', // Cache for 1 hour
			}
		});

	} catch (err) {
		console.error('Error serving file:', err);
		if (err instanceof jwt.JsonWebTokenError) {
			return new Response('Invalid token', { status: 401 });
		}
		return new Response('Internal server error', { status: 500 });
	}
};
