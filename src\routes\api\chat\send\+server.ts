import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Chat from '$lib/models/Chat';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import { encrypt } from '$lib/utils/encryption';
import r2Storage from '$lib/services/r2Storage';
import type { IAttachment } from '$lib/models/Chat';

export const POST: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
	}

	try {
		// Verify the JWT token first
		const decoded: any = jwt.verify(token, 'your-secret-key');

		await connect();

		const senderUser = await User.findById(decoded.userId);
		if (!senderUser) {
			return new Response(JSON.stringify({ error: 'Sender not found' }), { status: 404 });
		}

		let receiver: string | undefined;
		let groupId: string | undefined;
		let message: string;
		let attachments: IAttachment[] = [];

		// Check if request contains files (FormData) or just JSON
		const contentType = request.headers.get('content-type');

		if (contentType?.includes('multipart/form-data')) {
			// Handle FormData (with potential attachments)
			const formData = await request.formData();

			receiver = formData.get('receiver') as string || undefined;
			groupId = formData.get('groupId') as string || undefined;
			message = formData.get('message') as string || '';

			// Process attachments
			const files = formData.getAll('attachments') as File[];
			for (const file of files) {
				if (file && file.size > 0) {
					// Validate file
					const validation = r2Storage.validateFile(file, 'attachment');
					if (!validation.valid) {
						return new Response(JSON.stringify({ error: validation.error }), { status: 400 });
					}

					// Upload to R2
					const key = r2Storage.generateFileKey(decoded.userId, 'attachment', file.name);
					const uploadResult = await r2Storage.uploadFile(file, key, file.type, {
						userId: decoded.userId,
						uploadType: 'attachment'
					});

					attachments.push({
						key: uploadResult.key,
						url: uploadResult.url,
						filename: file.name,
						contentType: uploadResult.contentType,
						size: uploadResult.size
					});
				}
			}
		} else {
			// Handle JSON (text messages only)
			const body = await request.json();
			receiver = body.receiver;
			groupId = body.groupId;
			message = body.message;
		}

		if ((!message || !message.trim()) && attachments.length === 0) {
			return new Response(JSON.stringify({ error: 'Message or attachment required' }), { status: 400 });
		}

		if (!receiver && !groupId) {
			return new Response(JSON.stringify({ error: 'Receiver or group required' }), { status: 400 });
		}

		let newMessage;

		// The message will be automatically encrypted by the mongoose schema
		if (groupId) {
			newMessage = new Chat({
				sender: senderUser._id,
				group: groupId,
				message: message || '',
				attachments: attachments.length > 0 ? attachments : undefined
			});
		} else {
			const receiverUser = await User.findOne({ username: receiver });
			if (!receiverUser) {
				return new Response(JSON.stringify({ error: 'Receiver not found' }), { status: 404 });
			}

			// Debug logging
			console.log('Sender username:', senderUser.username);
			console.log('Receiver username:', receiverUser.username);
			console.log('Are they the same?', senderUser.username === receiverUser.username);

			if (senderUser.username === receiverUser.username) {
				return new Response(JSON.stringify({ error: 'Cannot send messages to yourself' }), {
					status: 400
				});
			}

			newMessage = new Chat({
				sender: senderUser._id,
				receiver: receiverUser._id,
				message: message || '',
				attachments: attachments.length > 0 ? attachments : undefined
			});
		}

		await newMessage.save();
		return new Response(JSON.stringify({ success: true }), { status: 200 });
	} catch (err) {
		console.error('Error sending message:', err);
		if (err instanceof jwt.JsonWebTokenError) {
			return new Response(JSON.stringify({ error: 'Invalid token' }), { status: 401 });
		}
		return new Response(JSON.stringify({ error: 'Failed to send message' }), { status: 500 });
	}
};
