<script lang="ts">
	import type { IAttachment } from '$lib/models/Chat';

	export let attachment: IAttachment;
	export let compact = false;

	$: isImage = attachment.contentType.startsWith('image/');
	$: isPdf = attachment.contentType === 'application/pdf';
	$: isArchive = ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'].includes(attachment.contentType);
	$: isDocument = attachment.contentType.includes('document') || attachment.contentType.includes('text/');

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	function getFileIcon() {
		if (isImage) return '🖼️';
		if (isPdf) return '📄';
		if (isArchive) return '📦';
		if (isDocument) return '📝';
		return '📎';
	}

	function downloadFile() {
		const link = document.createElement('a');
		link.href = attachment.url;
		link.download = attachment.filename;
		link.target = '_blank';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
</script>

{#if compact}
	<!-- Compact view for message list -->
	{#if isImage}
		<!-- Image preview in compact view -->
		<div class="max-w-xs">
			<button
				on:click={downloadFile}
				class="block w-full rounded-lg hover:opacity-90 transition-opacity focus:outline-none focus:ring-2 focus:ring-blue-500"
				title="Click to view full size: {attachment.filename}"
				aria-label="View image: {attachment.filename}"
			>
				<img
					src={attachment.url}
					alt={attachment.filename}
					class="max-h-48 w-full rounded-lg object-cover"
					loading="lazy"
				/>
			</button>
			<p class="mt-1 text-xs text-gray-500 text-center">
				{attachment.filename} ({formatFileSize(attachment.size)})
			</p>
		</div>
	{:else}
		<!-- Non-image files in compact view -->
		<div class="inline-flex items-center gap-1 rounded bg-gray-100 px-2 py-1 text-xs">
			<span>{getFileIcon()}</span>
			<button
				on:click={downloadFile}
				class="truncate text-blue-600 hover:text-blue-800 hover:underline"
				title={attachment.filename}
			>
				{attachment.filename}
			</button>
			<span class="text-gray-500">({formatFileSize(attachment.size)})</span>
		</div>
	{/if}
{:else}
	<!-- Full view -->
	<div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
		{#if isImage}
			<!-- Image preview -->
			<div class="mb-2">
				<img
					src={attachment.url}
					alt={attachment.filename}
					class="max-h-48 max-w-full rounded object-contain"
					loading="lazy"
				/>
			</div>
		{/if}

		<div class="flex items-center justify-between">
			<div class="flex items-center gap-2 min-w-0">
				<span class="text-lg">{getFileIcon()}</span>
				<div class="min-w-0">
					<p class="truncate font-medium text-sm" title={attachment.filename}>
						{attachment.filename}
					</p>
					<p class="text-xs text-gray-500">
						{formatFileSize(attachment.size)}
					</p>
				</div>
			</div>

			<button
				on:click={downloadFile}
				class="flex-shrink-0 rounded-full p-1.5 text-gray-500 transition-colors hover:bg-gray-100 hover:text-blue-600"
				title="Download {attachment.filename}"
				aria-label="Download {attachment.filename}"
			>
				<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
					<path stroke-linecap="round" stroke-linejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
				</svg>
			</button>
		</div>
	</div>
{/if}
