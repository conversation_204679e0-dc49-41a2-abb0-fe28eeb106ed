import mongoose from 'mongoose';

let isConnected = false;

export async function connect() {
	const mongoURI = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/konekt';

	// If already connected, return early
	if (isConnected && mongoose.connection.readyState === 1) {
		return;
	}

	try {
		await mongoose.connect(mongoURI, {
			serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
			socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
			bufferMaxEntries: 0, // Disable mongoose buffering
			bufferCommands: false, // Disable mongoose buffering
		});
		isConnected = true;
		console.log('MongoDB connected successfully');

		// Handle connection events
		mongoose.connection.on('error', (error) => {
			console.error('MongoDB connection error:', error);
			isConnected = false;
		});

		mongoose.connection.on('disconnected', () => {
			console.log('MongoDB disconnected');
			isConnected = false;
		});

	} catch (error) {
		console.error('MongoDB connection error:', error);
		isConnected = false;
		throw error;
	}
}
