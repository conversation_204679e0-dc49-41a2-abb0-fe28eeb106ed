import mongoose from 'mongoose';

let isConnected = false;

export async function connect() {
	const mongoURI = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/konekt';

	// If already connected, return early
	if (isConnected && mongoose.connection.readyState === 1) {
		return;
	}

	// If connection is in progress, wait for it
	if (mongoose.connection.readyState === 2) {
		return new Promise((resolve, reject) => {
			mongoose.connection.once('connected', resolve);
			mongoose.connection.once('error', reject);
		});
	}

	try {
		// Disconnect if in a bad state
		if (mongoose.connection.readyState !== 0) {
			await mongoose.disconnect();
		}

		await mongoose.connect(mongoURI, {
			serverSelectionTimeoutMS: 10000, // 10 seconds
			socketTimeoutMS: 45000,
		});

		// Set mongoose-specific options
		mongoose.set('bufferMaxEntries', 0);
		mongoose.set('bufferCommands', false);

		isConnected = true;
		console.log('MongoDB connected successfully');

		// Handle connection events (only set once)
		if (!mongoose.connection.listeners('error').length) {
			mongoose.connection.on('error', (error) => {
				console.error('MongoDB connection error:', error);
				isConnected = false;
			});

			mongoose.connection.on('disconnected', () => {
				console.log('MongoDB disconnected');
				isConnected = false;
			});

			mongoose.connection.on('reconnected', () => {
				console.log('MongoDB reconnected');
				isConnected = true;
			});
		}

	} catch (error) {
		console.error('MongoDB connection error:', error);
		isConnected = false;
		throw error;
	}
}
