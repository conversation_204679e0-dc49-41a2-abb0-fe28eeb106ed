import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface AuthState {
	token: string | null;
	username: string | null;
	isInitialized: boolean;
}

function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>({
		token: null,
		username: null,
		isInitialized: false
	});

	return {
		subscribe,
		setAuth: (token: string, username: string) => {
			set({ token, username, isInitialized: true });
		},
		clearAuth: () => {
			// Clear the cookie by setting it with an expired date
			if (browser) {
				document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
			}
			set({ token: null, username: null, isInitialized: true });
		},
		initializeAuth: async () => {
			if (!browser) return;

			try {
				const res = await fetch('/api/auth/validate', { credentials: 'include' });
				if (res.ok) {
					const data = await res.json();
					set({ token: 'validated', username: data.username, isInitialized: true });
				} else {
					set({ token: null, username: null, isInitialized: true });
				}
			} catch (err) {
				console.error('Error initializing auth:', err);
				set({ token: null, username: null, isInitialized: true });
			}
		},
		markInitialized: () => {
			update(state => ({ ...state, isInitialized: true }));
		}
	};
}

export const auth = createAuthStore();
