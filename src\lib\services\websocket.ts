import { browser } from '$app/environment';
import { auth } from '$lib/stores/auth';
import { get } from 'svelte/store';

export interface SSEMessage {
	type: string;
	[key: string]: any;
}

export interface ChatMessage {
	_id: string;
	message: string;
	sender: string;
	receiver?: string;
	group?: string;
	timestamp: string;
	attachments?: any[];
}

class RealtimeService {
	private eventSource: EventSource | null = null;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectDelay = 1000; // Start with 1 second
	private isConnecting = false;
	private messageHandlers = new Map<string, ((data: any) => void)[]>();
	private currentChatId: string | null = null;
	private currentChatType: 'user' | 'group' | null = null;

	constructor() {
		if (browser) {
			// Auto-connect when auth state changes
			auth.subscribe((authState) => {
				if (authState.isInitialized) {
					if (authState.username && authState.token) {
						this.connect(authState.token);
					} else {
						this.disconnect();
					}
				}
			});
		}
	}

	connect(token: string) {
		if (!browser || this.isConnecting || (this.eventSource && this.eventSource.readyState === EventSource.OPEN)) {
			return;
		}

		this.isConnecting = true;

		try {
			// Create SSE connection with token as query parameter
			const sseUrl = `/api/ws?token=${encodeURIComponent(token)}`;

			console.log('Connecting to SSE:', sseUrl);
			this.eventSource = new EventSource(sseUrl);

			this.eventSource.onopen = () => {
				console.log('SSE connected');
				this.isConnecting = false;
				this.reconnectAttempts = 0;
				this.reconnectDelay = 1000;
			};

			this.eventSource.onmessage = (event) => {
				try {
					const message: SSEMessage = JSON.parse(event.data);
					this.handleMessage(message);
				} catch (error) {
					console.error('Error parsing SSE message:', error);
				}
			};

			this.eventSource.onerror = (error) => {
				console.error('SSE error:', error);
				this.isConnecting = false;

				// EventSource will automatically reconnect, but we track attempts
				if (this.eventSource?.readyState === EventSource.CLOSED) {
					this.eventSource = null;
					if (this.reconnectAttempts < this.maxReconnectAttempts) {
						this.scheduleReconnect(token);
					}
				}
			};

		} catch (error) {
			console.error('Error creating SSE connection:', error);
			this.isConnecting = false;
		}
	}

	private scheduleReconnect(token: string) {
		this.reconnectAttempts++;
		const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

		console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

		setTimeout(() => {
			if (this.reconnectAttempts <= this.maxReconnectAttempts) {
				this.connect(token);
			}
		}, delay);
	}

	disconnect() {
		if (this.eventSource) {
			this.eventSource.close();
			this.eventSource = null;
		}
		this.isConnecting = false;
		this.reconnectAttempts = 0;
		this.currentChatId = null;
		this.currentChatType = null;
	}

	private handleMessage(message: SSEMessage) {
		console.log('Received SSE message:', message);

		// Call registered handlers for this message type
		const handlers = this.messageHandlers.get(message.type) || [];
		handlers.forEach(handler => {
			try {
				handler(message);
			} catch (error) {
				console.error('Error in message handler:', error);
			}
		});
	}

	// Register a handler for a specific message type
	on(messageType: string, handler: (data: any) => void) {
		if (!this.messageHandlers.has(messageType)) {
			this.messageHandlers.set(messageType, []);
		}
		this.messageHandlers.get(messageType)!.push(handler);

		// Return unsubscribe function
		return () => {
			const handlers = this.messageHandlers.get(messageType);
			if (handlers) {
				const index = handlers.indexOf(handler);
				if (index > -1) {
					handlers.splice(index, 1);
				}
			}
		};
	}

	// Join a specific chat (user or group)
	joinChat(chatId: string, chatType: 'user' | 'group') {
		this.currentChatId = chatId;
		this.currentChatType = chatType;
		// SSE doesn't need to send join messages - server broadcasts to all relevant users
	}

	// Leave current chat
	leaveChat() {
		this.currentChatId = null;
		this.currentChatType = null;
		// SSE doesn't need to send leave messages
	}

	// Get connection status
	get isConnected() {
		return this.eventSource && this.eventSource.readyState === EventSource.OPEN;
	}

	get connectionState() {
		if (!this.eventSource) return 'disconnected';

		switch (this.eventSource.readyState) {
			case EventSource.CONNECTING: return 'connecting';
			case EventSource.OPEN: return 'connected';
			case EventSource.CLOSED: return 'disconnected';
			default: return 'unknown';
		}
	}
}

// Create singleton instance
export const websocketService = new RealtimeService();
