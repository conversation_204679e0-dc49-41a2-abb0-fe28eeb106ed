// Centralized environment configuration
// This ensures consistent environment variable loading across the app

export const ENV = {
	// JWT Configuration
	JWT_SECRET: process.env.JWT_SECRET || 'fallback-secret',
	
	// MongoDB Configuration
	MONGODB_URI: process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/konekt',
	
	// Cloudflare R2 Configuration
	R2_ACCOUNT_ID: process.env.R2_ACCOUNT_ID,
	R2_ACCESS_KEY_ID: process.env.R2_ACCESS_KEY_ID,
	R2_SECRET_ACCESS_KEY: process.env.R2_SECRET_ACCESS_KEY,
	R2_BUCKET_NAME: process.env.R2_BUCKET_NAME || 'konekt',
	R2_PUBLIC_URL: process.env.R2_PUBLIC_URL,
	
	// Development flags
	NODE_ENV: process.env.NODE_ENV || 'development',
	
	// Validation helpers
	get isProduction() {
		return this.NODE_ENV === 'production';
	},
	
	get isDevelopment() {
		return this.NODE_ENV === 'development';
	},
	
	get isR2Configured() {
		return !!(
			this.R2_ACCOUNT_ID &&
			this.R2_ACCESS_KEY_ID &&
			this.R2_SECRET_ACCESS_KEY &&
			this.R2_PUBLIC_URL &&
			this.R2_ACCOUNT_ID !== 'demo_account_id' &&
			this.R2_ACCESS_KEY_ID !== 'demo_access_key' &&
			this.R2_ACCOUNT_ID !== 'dev_mode' &&
			this.R2_ACCESS_KEY_ID !== 'dev_mode' &&
			this.R2_ACCOUNT_ID.length > 10 &&
			this.R2_ACCESS_KEY_ID.length > 10
		);
	}
};

// Log configuration status (only in development)
if (ENV.isDevelopment) {
	console.log('🔧 Environment Configuration:');
	console.log(`   JWT_SECRET: ${ENV.JWT_SECRET ? 'configured' : 'using fallback'}`);
	console.log(`   MONGODB_URI: ${ENV.MONGODB_URI ? 'configured' : 'using default'}`);
	console.log(`   R2 Storage: ${ENV.isR2Configured ? '✅ configured' : '❌ not configured'}`);
	
	if (ENV.isR2Configured) {
		console.log(`   R2_ACCOUNT_ID: ${ENV.R2_ACCOUNT_ID?.substring(0, 8)}...`);
		console.log(`   R2_BUCKET_NAME: ${ENV.R2_BUCKET_NAME}`);
		console.log(`   R2_PUBLIC_URL: ${ENV.R2_PUBLIC_URL}`);
	}
}
